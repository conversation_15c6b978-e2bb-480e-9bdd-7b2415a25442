// 简单的语法测试脚本
const fs = require('fs');

try {
  const code = fs.readFileSync('bundle_1.js', 'utf8');
  
  // 尝试解析代码
  new Function(code);
  
  console.log('✅ JavaScript 语法检查通过！');
  console.log('文件可以被正确解析，应该可以在 JSNice 中正常工作。');
} catch (error) {
  console.log('❌ JavaScript 语法错误：');
  console.log(error.message);
  
  // 提取行号信息
  const match = error.message.match(/line (\d+)/i);
  if (match) {
    const lineNumber = parseInt(match[1]);
    console.log(`错误位置：第 ${lineNumber} 行`);
  }
}
